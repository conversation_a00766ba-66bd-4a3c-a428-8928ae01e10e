package service

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"path/filepath"
	"reflect"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/vima-inc/derental/mailer"
	"github.com/vima-inc/derental/models"
	csvparser "github.com/vima-inc/derental/parser/csv"
	excelparser "github.com/vima-inc/derental/parser/excel"
)

const (
	equipmentUploadPath = "equipments/"
	csvMimeType         = "text/csv"
	excelMimeType       = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	sheetName           = "Worksheet"
	startRow            = 2
	emptyStateImageURL  = "Empty_state_equipment.png"
)

// GetEquipmentByID returns a Equipment by id.
func (s *Service) GetEquipmentByID(ctx context.Context, id string) (models.Equipment, error) {
	return s.db.GetEquipmentByID(ctx, id)
}

// GetAllEquipmentsByEquipperID returns equipments by equipper id.
func (s *Service) GetAllEquipmentsByEquipperID(ctx context.Context, equipperID string) ([]models.Equipment, error) {
	return s.db.GetAllEquipmentsByEquipperID(ctx, equipperID)
}

// GetEquipmentsByEquipperID returns equipments by equipper id.
func (s *Service) GetEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error) {
	return s.db.GetEquipmentsByEquipperID(ctx, equipperID, limit, lastID)
}

// GetEquipmentByInternalID returns equipments by equipper id.
func (s *Service) GetEquipmentByInternalID(ctx context.Context, internalID string) (models.Equipment, error) {
	return s.db.GetEquipmentByInternalID(ctx, internalID)
}

// GetBookedEquipmentsByEquipperID returns booked equipments by equipper id.
func (s *Service) GetBookedEquipmentsByEquipperID(ctx context.Context, equipperID string, limit int, lastID string) ([]models.Equipment, error) {
	return s.db.GetBookedEquipmentsByEquipperID(ctx, equipperID, limit, lastID)
}

// GetAvailableEquipmentsByEquipperIDAndEquipmentNameORAlias returns available equipments by equipper_id & equipment name  or equipper_id & alias_en.
func (s *Service) GetAvailableEquipmentsByEquipperIDAndEquipmentNameORAlias(ctx context.Context, equipperID string, equipmentName string) ([]models.Equipment, error) {
	equipments, err := s.db.GetAvailableEquipmentsByEquipperIDAndEquipmentName(ctx, equipperID, equipmentName)
	if err != nil {
		return nil, fmt.Errorf("unable to get available equipments by equipper id and equipment name %w", err)
	}

	if len(equipments) != 0 {
		return equipments, nil
	}

	equipments, err = s.db.GetAvailableEquipmentsByEquipperIDAndAliasEN(ctx, equipperID, equipmentName)
	if err != nil {
		return nil, fmt.Errorf("unable to get available equipments by equipper id and alias en %w", err)
	}

	if len(equipments) != 0 {
		return equipments, nil
	}

	equipments, err = s.db.GetAvailableEquipmentsByEquipperIDAndAliasFR(ctx, equipperID, equipmentName)
	if err != nil {
		return nil, fmt.Errorf("unable to get available equipments by equipper id and alias fr %w", err)
	}

	return equipments, nil
}

// AddEquipment adds a new Equipment.
func (s *Service) AddEquipment(ctx context.Context, equipperID string, equipment models.Equipment) (models.Equipment, error) {
	equipper, err := s.db.GetEquipperByID(ctx, equipperID)
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to get equipper: %w", err)
	}

	equipment.CreatedAt = time.Now()
	equipment.CoverageArea = equipper.CoverageArea
	equipment.AvailableFrom = time.Now().UTC()
	// set in AvailableFrom hours midnight
	equipment.AvailableFrom = equipment.AvailableFrom.Truncate(24 * time.Hour)
	equipment.EquipperID = equipperID

	if !slices.Contains(equipment.Alias.EN, equipment.PreferredEquipmentName) && equipment.PreferredEquipmentName != "" && equipment.PreferredEquipmentName != equipment.NameEN {
		equipment.Alias.EN = append(equipment.Alias.EN, equipment.PreferredEquipmentName)
	}

	inventoryEquipment, err := s.db.GetEquipmentInventoryByName(ctx, equipment.NameEN)
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to get inventory equipment: %w", err)
	}

	inventoryEquipment.Alias.En = mergeSlices(inventoryEquipment.Alias.En, equipment.Alias.EN)
	err = s.db.UpdateToolerBidzEquipment(ctx, inventoryEquipment)
	if err != nil {
		return models.Equipment{}, fmt.Errorf("unable to update inventory equipment: %w", err)
	}

	return s.db.AddEquipment(ctx, equipment)
}

// UploadImageEquipment upload image new Equipment.
func (s *Service) UploadImageEquipment(ctx context.Context, equipmentID string, fileName string, data io.Reader) error {

	equipment, err := s.db.GetEquipmentByID(ctx, equipmentID)
	if err != nil {
		return fmt.Errorf("error getting equipment: %w", err)
	}
	path := fmt.Sprintf("equipment_library/%s/%s%s", equipment.EquipperID, equipmentID, filepath.Ext(fileName))

	imageURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
	if err != nil {
		return fmt.Errorf("failed to upload photo. Please try again. %w", err)
	}

	equipment.EquipperEquipmentPicture = imageURL
	err = s.db.UpdateEquipment(ctx, equipment)
	if err != nil {
		return fmt.Errorf("error updating equipper: %w", err)
	}
	return nil
}

// UpdateEquipment updates an existing Equipment.
func (s *Service) UpdateEquipment(ctx context.Context, equipperID string, equipment models.Equipment) error {

	equipment.UpdatedAt = time.Now()
	equipment.EquipperID = equipperID
	if !slices.Contains(equipment.Alias.EN, equipment.PreferredEquipmentName) && equipment.PreferredEquipmentName != "" {
		equipment.Alias.EN = append(equipment.Alias.EN, equipment.PreferredEquipmentName)
	}

	inventoryEquipment, err := s.db.GetEquipmentInventoryByName(ctx, equipment.NameEN)
	if err != nil {
		return fmt.Errorf("unable to get inventory equipment: %w", err)
	}

	inventoryEquipment.Alias.En = mergeSlices(inventoryEquipment.Alias.En, equipment.Alias.EN)
	err = s.db.UpdateToolerBidzEquipment(ctx, inventoryEquipment)
	if err != nil {
		return fmt.Errorf("unable to update inventory equipment: %w", err)
	}

	currentEquipment, err := s.db.GetEquipmentByID(ctx, equipment.ID)
	if err != nil {
		return fmt.Errorf("failed to get current equipment: %w", err)
	}

	changes := compareEquipmentChanges(currentEquipment, equipment)
	equipmentChange := models.EquipmentChange{
		EquipmentID:   equipment.ID,
		EquipmentName: equipment.NameEN,
		EquipperName:  equipperID,
		Fields:        changes,
		ChangedAt:     time.Now(),
	}

	go func() {
		err := s.sendEquipmentChangeNotification(ctx, equipment, equipmentChange)
		if err != nil {
			log.Printf("failed to send equipment change notification: %v", err)
		}
	}()

	return s.db.UpdateEquipment(ctx, equipment)
}

// UpdateEquipment updates an existing Equipment.
func (s *Service) UpdateBatchOfEquipmentByName(ctx context.Context, equipperID string, equipmentNames []string, minimumRentalPeriod int) error {
	var allEquipments []models.Equipment

	for _, name := range equipmentNames {
		equipments, err := s.db.GetAvailableEquipmentsByEquipperIDAndEquipmentName(ctx, equipperID, name)
		if err != nil {
			return fmt.Errorf("failed to get equipments for name %s: %w", name, err)
		}
		for _, equipment := range equipments {
			equipment.UpdatedAt = time.Now()
			equipment.EquipperID = equipperID
			equipment.MinimumRentalPeriod = minimumRentalPeriod
			allEquipments = append(allEquipments, equipment)
		}
	}

	if len(allEquipments) == 0 {
		return fmt.Errorf("no equipments found for the provided names")
	}

	if err := s.db.BatchUpdateEquipment(ctx, allEquipments); err != nil {
		return fmt.Errorf("failed to batch update equipments: %w", err)
	}

	return nil
}

// DeleteEquipment deletes an existing Equipment by id.
func (s *Service) DeleteEquipment(ctx context.Context, equipperID string, id string) error {
	equipment, err := s.db.GetEquipmentByID(ctx, id)
	if err != nil {
		return fmt.Errorf("unable to get equipment by id %w", err)
	}

	if equipment.EquipperID != equipperID {
		return fmt.Errorf("unable to delete equipment")
	}

	if equipment.Status == models.EquipmentBooked {
		return fmt.Errorf("unable to delete booked equipment")
	}

	pendingRequests, err := s.db.GetBookEquipmentByEquipmentID(ctx, equipment.ID)
	if err != nil {
		return fmt.Errorf("unable to get book equipment by equipment id %w", err)
	}

	if len(pendingRequests) != 0 {
		for _, pendingRequest := range pendingRequests {
			pendingRequest.Status = models.BookingCanceled

			err = s.db.UpdateBookEquipment(ctx, pendingRequest)
			if err != nil {
				return fmt.Errorf("unable to update book equipment %w", err)
			}

			err = s.SendCancelEmailToLodger(ctx, pendingRequest)
			if err != nil {
				return fmt.Errorf("unable to send cancel email to lodger %w", err)
			}
		}
	}

	equipment.IsActive = false

	return s.db.UpdateEquipment(ctx, equipment)
}

func (s *Service) SendCancelEmailToLodger(ctx context.Context, pendingRequest models.BookEquipment) error {
	lodger, err := s.db.GetLodgerByID(ctx, pendingRequest.LodgerID)
	if err != nil {
		return fmt.Errorf("unable to get lodger by id %w", err)
	}

	pendingRequest.CancelComment = "The equipment has been deleted by the equipper"

	if lodger.CommunicationPreferences == models.French {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridCancelBookingTemplateIDFR, []string{pendingRequest.LodgerEmail}, mailDataFromBookEquipmentFR(pendingRequest, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Booking canceled offer to lodger error: %w", err))
		}
	} else {
		err = s.mailer.SendFromTemplate(ctx, mailer.SendgridCancelBookingTemplateID, []string{pendingRequest.LodgerEmail}, mailDataFromBookEquipment(pendingRequest, s.frontendURL))
		if err != nil {
			log.Print(fmt.Errorf("unable to send Booking canceled offer to lodger error: %w", err))
		}
	}

	return nil
}

// DeleteEquipmentsByEquipperID deletes equipments by equipper id.
func (s *Service) DeleteEquipmentsByEquipperID(ctx context.Context, equipperID string) error {
	bookedequipment, err := s.db.GetAllBookedEquipmentsByEquipperID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("unable to get all booked equipments by equipper id %w", err)
	}

	if len(bookedequipment) != 0 {
		return fmt.Errorf("unable to delete equipments with booked equipment")
	}

	equipper, err := s.db.GetEquipperByID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("unable to get equipper by id %w", err)
	}

	equipper.HasInventory = false

	err = s.db.UpdateEquipper(ctx, equipper)
	if err != nil {
		return fmt.Errorf("unable to update equipper %w", err)
	}

	err = s.db.DeleteEquipmentsByEquipperID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("unable to delete equipments by equipper id %w", err)
	}

	return nil
}

func (s *Service) SendCSVReportMail(ctx context.Context, equipper models.Equipper, errorMessages []map[string]string) error {
	csvBytes, err := createCSVBytes(errorMessages)
	if err != nil {
		return fmt.Errorf("unable to create csv bytes: %w", err)
	}

	mailTemplateID := mailer.SendgridSendEquipperCSVReportID
	mailData := s.mailDataCsvReportFR(equipper, s.frontendURL)
	mailAttachment := s.mailDataCsvAttachement(csvBytes)

	err = s.mailer.SendFromTemplateWithAttachement(ctx, mailTemplateID, []string{equipper.Email}, mailData, mailAttachment)
	if err != nil {
		return fmt.Errorf("unable to send csv report to equipper: %w", err)
	}

	return nil
}

func createCSVBytes(messages []map[string]string) ([]byte, error) {
	b := &bytes.Buffer{}
	writer := csv.NewWriter(b)
	if err := writer.Write([]string{"Line", "Equipment name", "Persisted", "Field", "Error"}); err != nil {
		return nil, fmt.Errorf("failed to write CSV header: %w", err)
	}

	for i, m := range messages {
		equipment_name, equipment_nameOk := m["equipment name"]
		persisted, persistedOk := m["persisted"]
		line, lineOk := m["line"]
		field, fieldOk := m["field"]
		errorText, errorOk := m["error"]
		if lineOk && fieldOk && errorOk && equipment_nameOk && persistedOk {
			if err := writer.Write([]string{line, equipment_name, persisted, field, errorText}); err != nil {
				return nil, fmt.Errorf("failed to write CSV record for line %s: %w", line, err)
			}
		} else {
			fmt.Printf("Skipping map %d due to missing keys\n", i)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("error flushing CSV writer: %w", err)
	}
	return b.Bytes(), nil
}

type equipmentFile struct {
	ID                     string `excel:"Equipment ID"`
	PreferredEquipmentName string `excel:"Preferred Equipment Name"`
	Status                 string `excel:"Status"`
	NameEN                 string `excel:"Equipment Name [EN] @Derental"`
	Description            string `excel:"Name description [EN]"`
	DescriptionFR          string `excel:"Name description [FR]"`
	Brand                  string `excel:"Brand"`
	BrandModel             string `excel:"Brand model"`
	Weight                 string `excel:"Weight (lbs)"`
	Height                 string `excel:"Height (ft)"`
	Width                  string `excel:"Width (ft)"`
	Length                 string `excel:"Length (ft)"`
	Picture                string `excel:"Picture"`
	Force                  string `excel:"Force"`
	BTU                    string `excel:"BTU"`
	Volt                   string `excel:"Volt"`
	Watt                   string `excel:"Watt"`
	CFM                    string `excel:"CFM"`
	Capacity               string `excel:"Capacity"`
	Consumption            string `excel:"Consumption"`
	TypeOfPropulsion       string `excel:"Propulsion type"`
	PriceDay               string `excel:"Price per day"`
	PriceWeek              string `excel:"Price per week"`
	PriceMonth             string `excel:"Price per month"`
	DriveType              string `excel:"Drive type"`
	UsageHours             string `excel:"Usage hours"`
	SecurityDeposit        string `excel:"Security Deposit"`
	WeekendPrice           string `excel:"Weekend Price"`
	OneWaydelivery         string `excel:"One way delivery"`
	PlatformHeight         string `excel:"Platform height (ft)"`
	WorkingHeight          string `excel:"Working height (ft)"`
	HorizontalOutreach     string `excel:"Horizontal outreach (ft)"`
	PlatformCapacity       string `excel:"Platform capacity (lbs)"`
	PlatformDimension      string `excel:"Platform dimension (ft)"`
	PlatformExtension      string `excel:"Platform extension"`
	Diameter               string `excel:"Diameter"`
	ExtensionCapacity      string `excel:"Extension capacity (lbs)"`
	PlatformRotation       string `excel:"Platform rotation (degree)"`
	MachineRotation        string `excel:"Machine rotation (degree)"`
	MachineWidth           string `excel:"Machine width (ft)"`
	MachineLength          string `excel:"Machine length (ft)"`
	MachineHeight          string `excel:"Machine height (ft)"`
	ClosedMachineHeight    string `excel:"Closed machine height (ft)"`
	ClosedMachineLength    string `excel:"Closed machine length (ft)"`
	ClosedMachineWidth     string `excel:"Closed machine width (ft)"`
	BasketCapacity         string `excel:"Basket capacity"`
	CutDiameter            string `excel:"CutDiameter"`
	BasketLength           string `excel:"Basket length (ft)"`
	BasketWidth            string `excel:"Basket width (ft)"`
	LegsLocation           string `excel:"Legs location (ft)"`
	FloorHeight            string `excel:"Floor height (ft)"`
	CabinHeight            string `excel:"Cabin height (ft)"`
	Wheelbase              string `excel:"Wheelbase (ft)"`
	WheelSize              string `excel:"Wheel size (in)"`
	PlateDimension         string `excel:"Plate dimension"`
	Decibel                string `excel:"Decibel"`
	RollWidth              string `excel:"Roll width (ft)"`
	Compaction             string `excel:"Compaction"`
	Vibrations             string `excel:"Vibrations/min."`
	Lumen                  string `excel:"Lumen"`
	Pressure               string `excel:"Pressure"`
	Frequency              string `excel:"Frequency"`
	TiltingCapacity        string `excel:"Tilting capacity"`
	OperationCapacity      string `excel:"Operation capacity,"`
	TankCapacity           string `excel:"Tank capacity"`
	DiggingDepth           string `excel:"Digging depth"`
	DumpingHeight          string `excel:"Dumping height"`
	DiggingRadius          string `excel:"Digging radius"`
	TechnicalDataSheet     string `excel:"Technical data sheet"`
	EquipmentUsages        string `excel:"Usages of equipment"`
}

// AddEquipmentsFromFile adds equipments from a csv file.
func (s *Service) AddEquipmentsFromFile(ctx context.Context, path string) error {
	if !strings.HasPrefix(path, equipmentUploadPath) {
		return nil
	}

	reader, err := s.storage.Read(ctx, s.storage.DefaultBucket(), path)
	if err != nil {
		return fmt.Errorf("unable to read file with path %s: %w", path, err)
	}

	defer reader.Close()

	mimeType, fileReader, err := detectFileType(reader)
	if err != nil {
		return fmt.Errorf("unable to detect file type: %w", err)
	}

	equipperID := strings.Split(path, "/")[1]

	var equipments []models.Equipment
	var errorMessages []map[string]string

	switch mimeType {
	case csvMimeType:
		p := csvparser.New()

		err = p.Parse(fileReader, &equipments)
		if err != nil {
			go s.sendNotification(ctx, path, mimeType, map[string]string{
				"error": err.Error(),
			})

			return fmt.Errorf("unable to parse %s file: %w", path, err)
		}
	case excelMimeType:
		p, err := excelparser.New(sheetName, startRow, models.MappingFileUploadColumns())
		if err != nil {
			go s.sendNotification(ctx, path, mimeType, map[string]string{
				"error": err.Error(),
			})

			return fmt.Errorf("unable to create excel parser: %w", err)
		}

		var equipmentsFromFile []equipmentFile

		err = p.Parse(fileReader, &equipmentsFromFile)
		if err != nil {
			go s.sendNotification(ctx, path, mimeType, map[string]string{
				"error": err.Error(),
			})

			return fmt.Errorf("unable to parse %s file: %w", path, err)
		}

		equipments, errorMessages, err = s.mapEquipments(ctx, equipmentsFromFile, equipperID)
		if err != nil {
			return fmt.Errorf("unable to map equipments: %w", err)
		}

		if len(errorMessages) != 0 {
			err = s.generateCSVForErrors(ctx, errorMessages, equipperID)
			if err != nil {
				log.Printf("unable to generate csv for errors: %v", err)
			}
		}
	default:
		go s.sendNotification(ctx, path, mimeType, map[string]string{
			"error": fmt.Sprintf("unknown mime type: %s err: %s", mimeType, err),
		})

		return fmt.Errorf("unable to parse file with path %s: %w", path, err)
	}

	err = AddEquipments(ctx, s, equipperID, path, mimeType, equipments)
	if err != nil {
		return fmt.Errorf("unable to add equipments: %w", err)
	}

	return nil
}

func (s *Service) generateCSVForErrors(ctx context.Context, errorMessages []map[string]string, equipperID string) error {
	equipper, err := s.GetEquipperByID(ctx, equipperID)
	if err != nil {
		return fmt.Errorf("unable to get equipper with id %s: %w", equipperID, err)
	}

	err = s.SendCSVReportMail(ctx, equipper, errorMessages)
	if err != nil {
		return fmt.Errorf("unable to send csv report to equipper: %w", err)
	}

	return nil
}

func AddEquipments(ctx context.Context, s *Service, equipperID string, path string, mimeType string, equipments []models.Equipment) error {
	equipper, err := s.db.GetEquipperByID(ctx, equipperID)
	if err != nil {
		go s.sendNotification(ctx, path, mimeType, map[string]string{
			"error": fmt.Sprintf("unable to get equipper with id %s: %v", equipperID, err),
		})

		return fmt.Errorf("unable to get equipper with id %s: %w", equipperID, err)
	}

	equipper.HasInventory = true

	err = s.db.UpdateEquipper(ctx, equipper)
	if err != nil {
		return fmt.Errorf("can not update equipper error %w", err)
	}

	inventoryEquipments, err := s.db.GetAllToolerBidzEquipment(ctx)
	if err != nil {
		return fmt.Errorf("unable to get all bidz equipments: %w", err)
	}

	for i, equipment := range equipments {
		for i, a := range equipment.Alias.EN {
			equipment.Alias.EN[i] = strings.TrimSpace(a)
		}

		for i, a := range equipment.Alias.FR {
			equipment.Alias.FR[i] = strings.TrimSpace(a)
		}

		aliasesID := s.mapInventoryEquipmentAlias(equipment, inventoryEquipments)

		equipments[i].EquipperID = equipperID
		equipments[i].Address.Address = equipper.Address.Address
		equipments[i].Address.City = equipper.Address.City
		equipments[i].Address.Country = equipper.Address.Country
		equipments[i].Address.ZipCode = equipper.Address.ZipCode
		equipments[i].CoverageArea = append(equipments[i].CoverageArea, equipper.CoverageArea...)
		equipments[i].EquipperEmail = equipper.Email
		equipments[i].EquipperName = equipper.Company
		equipments[i].AliasID = aliasesID
		equipments[i].AvailableFrom = time.Now().UTC()
		// set in AvailableFrom hours midnight
		equipments[i].AvailableFrom = equipments[i].AvailableFrom.Truncate(24 * time.Hour)
		equipments[i].Price.Currency = equipper.Currency
		equipments[i].InternalID = equipments[i].InternalID + "|||" + equipperID
		eq, err := s.GetEquipmentByInternalID(ctx, equipments[i].InternalID)
		if err == nil {
			equipments[i].ID = eq.ID
		}
	}

	err = s.db.BatchAddEquipment(ctx, equipments)
	if err != nil {
		go s.sendNotification(ctx, path, mimeType, map[string]string{
			"error": fmt.Sprintf("unable to add equipment for equipper with id %s: %v", equipperID, err),
		})

		return fmt.Errorf("unable to add equipment: %w", err)
	}

	return nil
}

// SaveEquipmentsFile saves equipments to file.
func (s *Service) SaveEquipmentsFile(ctx context.Context, equipperID string, fileName string, data io.Reader) (string, error) {
	path := fmt.Sprintf("equipments/%s/%s", equipperID, fileName)

	return s.storage.Write(ctx, s.storage.DefaultBucket(), path, data)
}

// GetEquipmentsByName return equipments by name.
func (s *Service) GetEquipmentsByName(ctx context.Context, equipmentName string) ([]models.Equipment, error) {
	return s.db.GetEquipmentByName(ctx, equipmentName)
}

// GetEquipmentInventoryByName return equipment inventory by name.
func (s *Service) GetEquipmentInventoryByName(ctx context.Context, equipmentName string) (models.ToolerBidzEquipment, error) {
	return s.db.GetEquipmentInventoryByName(ctx, equipmentName)
}

func identifyFieldMismatches(ef equipmentFile, masterEq models.ToolerBidzEquipment) []string {
	mismatches := []string{}

	if masterEq.NameEN != ef.NameEN {
		mismatches = append(mismatches, "NameEN")
	}

	return mismatches
}

// processAndUploadImage process the image link from Excel file and upload to bucket if exist
func (s *Service) processAndUploadImage(ctx context.Context, ef equipmentFile, mastereq models.ToolerBidzEquipment, equipperID string) (string, error) {
	pictureURL := mastereq.ImageLink

	response, err := http.Get(ef.Picture)
	if err != nil || response.StatusCode != http.StatusOK {
		return pictureURL, fmt.Errorf("failed to fetch image from URL: %w", err)
	}
	defer response.Body.Close()

	imageURL, err := s.UploadEquipmentPhoto(ctx, equipperID, ef.ID, response.Body)
	if err != nil {
		return pictureURL, err
	}

	return imageURL, nil
}

// Updated UploadEquipmentPhoto function signature and implementation
func (s *Service) UploadEquipmentPhoto(ctx context.Context, equipperID string, internalID string, data io.Reader) (string, error) {
	imagePath := fmt.Sprintf("equipment_library/%s/%s", equipperID, internalID)
	photoURL, err := s.storage.Write(ctx, s.storage.DefaultBucket(), imagePath, data)
	if err != nil {
		return "", fmt.Errorf("failed to upload photo. Please try again. %w", err)
	}
	return photoURL, nil
}

// cleanSplit  cleans a table
func (s *Service) cleanSplit(input string, sep string) []string {
	splitData := strings.Split(input, sep)
	cleanedData := []string{}

	for _, item := range splitData {
		if item != "0" && item != "" {
			cleanedData = append(cleanedData, item)
		}
	}

	return cleanedData
}

// checkPreferredEquipmentNameInAliases  checks if PreferredEquipmentName exists in the masterInventory
func (s *Service) checkPreferredEquipmentNameInAliases(preferredName string, aliasEn []string) bool {
	for _, alias := range aliasEn {
		if preferredName == alias {
			return true
		}
	}
	return false
}

// mapEquipments parsing equipments file and return equipments.
func (s *Service) mapEquipments(ctx context.Context, equipmentsFromFile []equipmentFile, equipperID string) ([]models.Equipment, []map[string]string, error) {
	equipments := []models.Equipment{}
	var aliasEn []string
	var errorMessages []map[string]string
	masterInventory, err := s.GetAllToolerBidzEquipment(ctx)
	if err != nil {
		return nil, nil, err
	}

	for i, ef := range equipmentsFromFile {
		var currentErrors []map[string]string

		allempty := validateEquipmentFieldsEmpty(ef)
		if allempty {
			continue
		}

		prices, errMsg := s.parsePrices(ef, i)
		if errMsg != nil {
			currentErrors = append(currentErrors, errMsg...)
		}

		validationError := validateEquipmentFields(ef, i)
		if validationError != nil {
			currentErrors = append(currentErrors, validationError...)
		}

		var masterEq models.ToolerBidzEquipment

		driveType := s.cleanSplit(ef.DriveType, ",")

		found := false
		for _, master := range masterInventory {
			if ef.NameEN == master.NameEN {
				masterEq = master
				found = true
				break
			}
		}

		if !found {
			currentErrors = append(currentErrors, map[string]string{
				"equipment name": ef.NameEN,
				"persisted":      "False",
				"line":           fmt.Sprintf("%d", i+3),
				"field":          "No Match",
				"error":          "No matching entry in master inventory for NameEN: " + ef.NameEN,
			})
		} else {
			mismatches := identifyFieldMismatches(ef, masterEq)
			if len(mismatches) > 0 {
				currentErrors = append(currentErrors, map[string]string{
					"equipment name": ef.NameEN,
					"persisted":      "False",
					"line":           fmt.Sprintf("%d", i+3),
					"field":          strings.Join(mismatches, ", "),
					"error":          "Mismatch in fields",
				})
			} else {
				masterEq, err := s.db.GetEquipmentInventoryByName(ctx, ef.NameEN)
				if !s.checkPreferredEquipmentNameInAliases(ef.PreferredEquipmentName, masterEq.Alias.En) && ef.PreferredEquipmentName != ef.NameEN {
					if err != nil {
						return nil, nil, fmt.Errorf("unable to get inventory equipment: %w", err)
					}
					masterEq.Alias.En = append(masterEq.Alias.En, ef.PreferredEquipmentName)
					err = s.db.UpdateToolerBidzEquipment(ctx, masterEq)
					if err != nil {
						return nil, nil, fmt.Errorf("unable to update inventory equipment: %w", err)
					}
				}
				aliasEn = masterEq.Alias.En
			}

		}

		imageLink, err := s.processAndUploadImage(ctx, ef, masterEq, equipperID)
		if err != nil && ef.Picture != "" {
			currentErrors = append(currentErrors, map[string]string{
				"equipment name": ef.NameEN,
				"persisted":      "True",
				"line":           fmt.Sprintf("%d", i+3),
				"field":          "Picture",
				"error":          fmt.Sprintf("Invalid value. Please recheck the input and try again. Details: %s", err.Error()),
			})
		}

		if len(currentErrors) > 0 {
			errorMessages = append(errorMessages, currentErrors...)
			continue
		}

		newEquipment := models.Equipment{
			InternalID:             ef.ID,
			PreferredEquipmentName: ef.PreferredEquipmentName,
			NameEN:                 ef.NameEN,
			Description:            ef.Description,
			SubCategory:            masterEq.SubCategory,
			Category:               masterEq.Category,
			Brand:                  ef.Brand,
			BrandModel:             ef.BrandModel,
			DriveType:              driveType,
			Weight:                 ef.Weight,
			Height:                 ef.Height,
			Width:                  ef.Width,
			Length:                 ef.Length,
			Diameter:               ef.Diameter,
			CutDiameter:            ef.CutDiameter,
			Force:                  ef.Force,
			UsageHours:             ef.UsageHours,
			BTU:                    ef.BTU,
			Volt:                   ef.Volt,
			Watt:                   ef.Watt,
			CFM:                    ef.CFM,
			Capacity:               ef.Capacity,
			Consumption:            ef.Consumption,
			TypeOfPropulsion:       strings.Split(ef.TypeOfPropulsion, ","),
			PlatformHeight:         ef.PlatformHeight,
			WorkingHeight:          ef.WorkingHeight,
			HorizontalOutreach:     ef.HorizontalOutreach,
			PlatformCapacity:       ef.PlatformCapacity,
			PlatformDimension:      ef.PlatformDimension,
			PlatformExtension:      ef.PlatformExtension,
			ExtensionCapacity:      ef.ExtensionCapacity,
			PlatformRotation:       ef.PlatformRotation,
			MachineRotation:        ef.MachineRotation,
			MachineWidth:           ef.MachineWidth,
			MachineLength:          ef.MachineLength,
			MachineHeight:          ef.MachineHeight,
			ClosedMachineHeight:    ef.ClosedMachineHeight,
			ClosedMachineLength:    ef.ClosedMachineLength,
			ClosedMachineWidth:     ef.ClosedMachineWidth,
			BasketCapacity:         ef.BasketCapacity,
			BasketLength:           ef.BasketLength,
			BasketWidth:            ef.BasketWidth,
			LegsLocation:           ef.LegsLocation,
			FloorHeight:            ef.FloorHeight,
			CabinHeight:            ef.CabinHeight,
			Wheelbase:              ef.Wheelbase,
			WheelSize:              ef.WheelSize,
			PlateDimension:         ef.PlateDimension,
			Decibel:                ef.Decibel,
			RollWidth:              ef.RollWidth,
			Compaction:             ef.Compaction,
			Vibrations:             ef.Vibrations,
			Lumen:                  ef.Lumen,
			Pressure:               ef.Pressure,
			Frequency:              ef.Frequency,
			TiltingCapacity:        ef.TiltingCapacity,
			OperationCapacity:      ef.OperationCapacity,
			TankCapacity:           ef.TankCapacity,
			DiggingDepth:           ef.DiggingDepth,
			DumpingHeight:          ef.DumpingHeight,
			DiggingRadius:          ef.DiggingRadius,
			TechnicalDataSheet:     ef.TechnicalDataSheet,
			EquipmentUsages:        ef.EquipmentUsages,
			ImageLink:              imageLink,
			AvgRating:              0,
			TotalComments:          0,
			Status:                 models.EquipmentAvailable,
			CreatedAt:              time.Now(),
			UpdatedAt:              time.Now(),
			IsActive:               true,
			Alias: models.EquipmentAlias{
				EN: aliasEn,
			},
			Price: models.Price{
				Day:              prices["day"],
				Week:             prices["week"],
				Month:            prices["month"],
				Deposit:          prices["securityDeposit"],
				DeliveryDropCost: prices["oneWayDelivery"],
				WeekendPrice:     prices["weekendPrice"],
			},
		}

		equipments = append(equipments, newEquipment)
	}

	if len(errorMessages) > 0 {
		log.Println("Errors occurred while processing equipment files:", errorMessages)
	}

	return equipments, errorMessages, nil
}

func validateEquipmentFieldsEmpty(ef equipmentFile) bool {

	requiredFields := map[string]string{
		"InternalID": ef.ID,
		"NameEN":     ef.NameEN,
		"PriceDay":   ef.PriceDay,
		"PriceWeek":  ef.PriceWeek,
		"PriceMonth": ef.PriceMonth,
	}

	allEmpty := true
	for _, value := range requiredFields {
		if value != "" && value != "#N/A" {
			allEmpty = false
			break
		}
	}

	return allEmpty
}
func validateEquipmentFields(ef equipmentFile, i int) []map[string]string {
	var errorMessages []map[string]string

	requiredFields := map[string]string{
		"InternalID": ef.ID,
		"NameEN":     ef.NameEN,
		"PriceDay":   ef.PriceDay,
		"PriceWeek":  ef.PriceWeek,
		"PriceMonth": ef.PriceMonth,
	}

	for field, value := range requiredFields {
		if value == "" {
			errorMessages = append(errorMessages, map[string]string{
				"equipment name": ef.NameEN,
				"persisted":      "False",
				"line":           fmt.Sprintf("%d", i+3),
				"field":          field,
				"error":          "missing value",
			})
		}
	}

	if len(errorMessages) > 0 {
		return errorMessages
	}

	return nil
}

func (s *Service) parsePrices(ef equipmentFile, i int) (map[string]float64, []map[string]string) {
	prices := make(map[string]float64)
	var errorMessages []map[string]string

	mandatoryPriceKeys := map[string]string{
		"day":   "PriceDay",
		"week":  "PriceWeek",
		"month": "PriceMonth",
	}
	optionalPriceKeys := map[string]string{
		"securityDeposit": "SecurityDeposit",
		"oneWayDelivery":  "OneWaydelivery",
		"weekendPrice":    "WeekendPrice",
	}

	cleanAndConvert := func(str string) (float64, error) {
		cleanStr := strings.ReplaceAll(str, "$", "")
		return strconv.ParseFloat(cleanStr, 64)
	}

	for key, field := range mandatoryPriceKeys {
		fieldValue := reflect.ValueOf(ef).FieldByName(field).String()
		value, err := cleanAndConvert(fieldValue)
		if err != nil {
			errorMessages = append(errorMessages, map[string]string{
				"equipment name": ef.NameEN,
				"persisted":      "False",
				"line":           fmt.Sprintf("%d", i+3),
				"field":          field,
				"error":          fmt.Sprintf("Invalid value: %s. Please recheck the input and try again. Details: %s", fieldValue, err.Error()),
			})
		} else {
			prices[key] = value
		}
	}

	for key, field := range optionalPriceKeys {
		fieldValue := reflect.ValueOf(ef).FieldByName(field).String()
		if fieldValue != "" {
			value, err := cleanAndConvert(fieldValue)
			if err != nil {
				errorMessages = append(errorMessages, map[string]string{
					"equipment name": ef.NameEN,
					"persisted":      "False",
					"line":           fmt.Sprintf("%d", i+3),
					"field":          field,
					"error":          fmt.Sprintf("Invalid value: %s. Please recheck the input and try again. Details: %s", fieldValue, err.Error()),
				})
			} else {
				prices[key] = value
			}
		}
	}

	if len(errorMessages) > 0 {
		return nil, errorMessages
	}

	return prices, nil
}

// ChangeEquipmentStatus change equipment status.
func (s *Service) ChangeEquipmentStatus(ctx context.Context, equipmentID string, status models.EquipmentStatus) error {
	return s.db.ChangeEquipmentStatus(ctx, equipmentID, status)
}

func (s *Service) sendNotification(ctx context.Context, path string, fileType string, params map[string]string) {
	var p strings.Builder
	if len(params) > 0 {
		p.WriteString("Parameters: \n")

		for k, v := range params {
			p.WriteString(fmt.Sprintf("➡️ `%s` : `%s`\n", k, v))
		}
	}

	now := time.Now()
	msg := fmt.Sprintf("❌ *Impossible d'uploader le fichier %q de type %s le %s *\n %s", path, fileType, now.Format("_2 Jan 15:04"), p.String())

	err := s.notifier.Send(ctx, msg)
	if err != nil {
		log.Printf("unable to send notification: %v", err)
	}
}

func (s *Service) mapInventoryEquipmentAlias(equipment models.Equipment, inventoryEquipments []models.ToolerBidzEquipment) string {
	en := equipment.Alias.EN
	inventoryEquipmentID := ""

	for i, inventoryEquipment := range inventoryEquipments {
		if inventoryEquipment.NameEN == equipment.NameEN {
			en = appendIfNotExist(en, inventoryEquipment.Alias.En...)
			inventoryEquipments[i].Alias.En = en
			inventoryEquipmentID = inventoryEquipment.ID
		}
	}

	return inventoryEquipmentID
}

func detectFileType(reader io.Reader) (string, io.Reader, error) {
	buff := bytes.NewBuffer(nil)

	mtype, err := mimetype.DetectReader(io.TeeReader(reader, buff))
	if err != nil {
		return "", nil, fmt.Errorf("unable to detect file type: %w", err)
	}

	recycled := io.MultiReader(buff, reader)

	return mtype.String(), recycled, nil
}

func getPictureURL(equipmentName string, equipmentLibraryURL string) string {
	if equipmentName == "" {
		return fmt.Sprintf("%s/%s", equipmentLibraryURL, emptyStateImageURL)
	}

	return fmt.Sprintf("%s/%s", equipmentLibraryURL, url.PathEscape(strings.TrimSpace(equipmentName)))
}

// removeEmptyString remove empty string from slice.
func removeEmptyString(s []string) []string {
	var r []string

	for _, str := range s {
		if str != "" {
			r = append(r, str)
		}
	}

	return r
}

func appendIfNotExist[T comparable](s []T, v ...T) []T {
	if len(v) == 0 {
		return s
	}

	if len(s) == 0 {
		return v
	}

	for _, x := range v {
		if !slices.Contains(s, x) {
			s = append(s, x)
		}
	}

	return s
}

func mergeSlices(a, b []string) []string {
	m := make(map[string]struct{}, len(a))

	for _, item := range a {
		m[item] = struct{}{}
	}

	for _, item := range b {
		if _, ok := m[item]; !ok {
			a = append(a, item)
		}
	}
	return a
}

func (s *Service) ProcessEquipmentBookingsAndNotifyEquippers(ctx context.Context) error {
	log.Printf("Starting ProcessEquipmentBookingsAndNotifyEquippers")

	bookEquipments, err := s.db.GetBookEquipmentByStatusAndEndDate(ctx)
	if err != nil {
		return fmt.Errorf("unable to retrieve bookEquipments: %w", err)
	}

	if len(bookEquipments) == 0 {
		log.Printf("No bookEquipments found with the given criteria")

		return nil
	}

	log.Printf("Found %d bookEquipments", len(bookEquipments))

	equipmentMap := make(map[string][]models.Equipment)

	for _, booking := range bookEquipments {
		equipment, err := s.db.GetEquipmentByID(ctx, booking.EquipmentID)
		if err != nil {
			return fmt.Errorf("unable to retrieve equipment by ID %s: %w", booking.EquipmentID, err)
		}
		equipmentMap[booking.EquipperID] = append(equipmentMap[booking.EquipperID], equipment)
	}

	emailsSent := 0

	for equipperID, equipments := range equipmentMap {
		log.Printf("Sending update email to equipper ID %s", equipperID)

		if err := s.SendAutomaicUpdateEmailToEquipper(ctx, equipperID, equipments); err != nil {
			return fmt.Errorf("unable to send email for equipper ID %s: %w", equipperID, err)
		}

		emailsSent++
	}

	log.Printf("Successfully sent notifications to all equippers. Emails sent: %d", emailsSent)
	return nil
}

func (s *Service) SendAutomaicUpdateEmailToEquipper(ctx context.Context, id string, equipments []models.Equipment) error {
	log.Printf("Sending automatic update email to equipper ID %s", id)

	equipper, err := s.db.GetEquipperByID(ctx, id)
	if err != nil {
		return fmt.Errorf("unable to get equipper by ID %s: %w", id, err)
	}

	template := mailer.SendgridSendAutomaticUpdateTemplateIDFR
	mailData := s.mailDataFromEquipmentFR(equipments, s.frontendURL)
	if equipper.CommunicationPreferences == models.English {
		mailData = s.mailDataFromEquipment(equipments, s.frontendURL)
		template = mailer.SendgridSendAutomaticUpdateTemplateID
	}

	err = s.mailer.SendFromTemplateWithObject(ctx, template, []string{equipper.Email}, mailData)
	if err != nil {
		return fmt.Errorf("unable to send email to equipper ID %s: %w", id, err)
	}

	log.Printf("Successfully sent email to equipper ID %s", id)
	return nil
}

func (s *Service) mailDataFromEquipmentFR(equipments []models.Equipment, frontendURL string) map[string]any {
	equipmentDetails := make([]map[string]string, len(equipments))
	var equipper_name string
	for i, equipment := range equipments {
		equipper_name = equipment.EquipperName
		equipmentDetails[i] = map[string]string{
			"internal_id":     strings.Split(equipment.InternalID, "|||")[0],
			"equipment_name":  equipment.NameFR,
			"description":     equipment.DescriptionFR,
			"equipment_image": equipment.ImageLink,
		}
	}

	onClickLink := fmt.Sprintf("%s/equipperManagementPortal/equipmentManagement?booked=true", frontendURL)
	mailData := map[string]any{
		"on_click":       onClickLink,
		"equipment_list": equipmentDetails,
		"equipper_name":  equipper_name,
	}

	log.Printf("[%s] Prepared mail data for French version", time.Now().Format(time.RFC3339))
	return mailData
}

func (s *Service) mailDataFromEquipment(equipments []models.Equipment, frontendURL string) map[string]any {
	equipmentDetails := make([]map[string]string, len(equipments))
	var equipper_name string
	for i, equipment := range equipments {
		equipper_name = equipment.EquipperName
		equipmentDetails[i] = map[string]string{
			"internal_id":     strings.Split(equipment.InternalID, "|||")[0],
			"equipment_name":  equipment.NameEN,
			"description":     equipment.Description,
			"equipment_image": equipment.ImageLink,
		}
	}

	onClickLink := fmt.Sprintf("%s/equipperManagementPortal/equipmentManagement?booked=true", frontendURL)
	mailData := map[string]any{
		"on_click":       onClickLink,
		"equipment_list": equipmentDetails,
		"equipper_name":  equipper_name,
	}

	log.Printf("[%s] Prepared mail data for English version", time.Now().Format(time.RFC3339))
	return mailData
}
func (s *Service) mailDataCsvAttachement(csvFile []byte) map[string][]byte {

	return map[string][]byte{
		"inventory-upload-errors-report.csv": csvFile,
	}
}

func (s *Service) mailDataCsvReportFR(equipper models.Equipper, frontendURL string) map[string]string {
	onClickLink := fmt.Sprintf("%s/equipperManagementPortal/equipmentManagement", frontendURL)
	return map[string]string{

		"on_click": onClickLink,
		"company":  equipper.Company,
	}
}

func compareEquipmentChanges(old, new models.Equipment) []models.ChangeFields {
	changes := make([]models.ChangeFields, 0)

	// Compare basic fields
	if old.NameEN != new.NameEN {
		changes = append(changes, models.ChangeFields{
			Field:    "name",
			OldValue: old.NameEN,
			NewValue: new.NameEN,
		})
	}

	if old.Description != new.Description {
		changes = append(changes, models.ChangeFields{
			Field:    "description",
			OldValue: old.Description,
			NewValue: new.Description,
		})
	}

	if old.PreferredEquipmentName != new.PreferredEquipmentName {
		changes = append(changes, models.ChangeFields{
			Field:    "preferred_equipment_name",
			OldValue: old.PreferredEquipmentName,
			NewValue: new.PreferredEquipmentName,
		})
	}

	if old.EquipperEquipmentPicture != new.EquipperEquipmentPicture {
		changes = append(changes, models.ChangeFields{
			Field:    "equipper_equipment_picture",
			OldValue: old.EquipperEquipmentPicture,
			NewValue: new.EquipperEquipmentPicture,
		})
	}

	// Compare price fields
	if old.Price.Day != new.Price.Day {
		changes = append(changes, models.ChangeFields{
			Field:    "price_day",
			OldValue: fmt.Sprintf("%v", old.Price.Day),
			NewValue: fmt.Sprintf("%v", new.Price.Day),
		})
	}
	if old.Price.Week != new.Price.Week {
		changes = append(changes, models.ChangeFields{
			Field:    "price_week",
			OldValue: fmt.Sprintf("%v", old.Price.Week),
			NewValue: fmt.Sprintf("%v", new.Price.Week),
		})
	}
	if old.Price.Month != new.Price.Month {
		changes = append(changes, models.ChangeFields{
			Field:    "price_month",
			OldValue: fmt.Sprintf("%v", old.Price.Month),
			NewValue: fmt.Sprintf("%v", new.Price.Month),
		})
	}

	// Compare technical specifications
	if old.Brand != new.Brand {
		changes = append(changes, models.ChangeFields{
			Field:    "brand",
			OldValue: old.Brand,
			NewValue: new.Brand,
		})
	}
	if old.BrandModel != new.BrandModel {
		changes = append(changes, models.ChangeFields{
			Field:    "brand_model",
			OldValue: old.BrandModel,
			NewValue: new.BrandModel,
		})
	}
	if old.Weight != new.Weight {
		changes = append(changes, models.ChangeFields{
			Field:    "weight",
			OldValue: old.Weight,
			NewValue: new.Weight,
		})
	}
	if old.Height != new.Height {
		changes = append(changes, models.ChangeFields{
			Field:    "height",
			OldValue: old.Height,
			NewValue: new.Height,
		})
	}
	if old.Width != new.Width {
		changes = append(changes, models.ChangeFields{
			Field:    "width",
			OldValue: old.Width,
			NewValue: new.Width,
		})
	}
	if old.Length != new.Length {
		changes = append(changes, models.ChangeFields{
			Field:    "length",
			OldValue: old.Length,
			NewValue: new.Length,
		})
	}

	return changes
}

func (s *Service) sendEquipmentChangeNotification(ctx context.Context, equipment models.Equipment, changes models.EquipmentChange) error {
	fields := make(map[string]any)
	for _, change := range changes.Fields {
		fields[change.Field] = change
	}

	mailData := map[string]any{
		"equipment_name": equipment.NameEN,
		"equipment_id":   equipment.ID,
		"fields":         fields,
	}

	if err := s.mailer.SendFromTemplateWithObject(ctx, mailer.SendEquipmentChangeTemplateID, []string{"<EMAIL>"}, mailData); err != nil {
		return fmt.Errorf("failed to send email notification: %w", err)
	}

	return nil
}
