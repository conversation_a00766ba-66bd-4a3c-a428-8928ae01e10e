import recommend from '@algolia/recommend';
import { RelatedProducts } from '@algolia/recommend-react';
import { HorizontalSlider } from '@algolia/ui-components-horizontal-slider-react';
import '@algolia/ui-components-horizontal-slider-theme';
import React from 'react';
import EquipperSpotlightEquipmentsItem from '../../shared/components/equipment/Equipper_spotlight_equipments_item';

const recommendClient = recommend(
  `${import.meta.env.VITE_ALGOLIA_ID}`,
  `${import.meta.env.VITE_ALGOLIA_API_KEY}`
);

function ItemRelated({ item }) {
  return (
    <EquipperSpotlightEquipmentsItem hit={item} isRecommendationsSection />
  );
}

export default function RecommendationsSection({ objectID }) {
  if (objectID === '' || !objectID) {
    return null;
  }
  return (
    <RelatedProducts
      recommendClient={recommendClient}
      indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
      objectIDs={[objectID]}
      itemComponent={ItemRelated}
      view={HorizontalSlider}
      maxRecommendations={8}
      translations={{
        title: ' '
      }}
      queryParameters= {
     	{filters: "(NOT \"coverage_area\":\"LaSalle, Quebec\")"}
    }
    />
  );
}
